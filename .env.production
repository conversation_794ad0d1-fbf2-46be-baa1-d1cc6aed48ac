# Production Environment Variables for Voice Bot

# Required API Keys
GROQ_API_KEY=your_groq_api_key_here

# Server Configuration
HOST=0.0.0.0
PORT=10000
BACKEND_PORT=8000
FRONTEND_PORT=8504

# SSL/HTTPS Configuration
USE_SSL=true
USE_HTTPS=true
ALLOW_SELF_SIGNED_CERTS=false

# Environment
ENVIRONMENT=production
DEBUG=false

# CORS Configuration
FRONTEND_URL=https://your-app-name.onrender.com

# Performance Settings
WORKERS=1
MAX_WORKERS=2

# Logging
LOG_LEVEL=info

# Optional: TTS Provider Configuration
# AZURE_SPEECH_KEY=your_azure_key
# AZURE_SPEECH_REGION=your_region

# Optional: Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60

# Optional: Memory Management
MAX_MEMORY_MB=512
CACHE_SIZE_LIMIT=100
